import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';
import { httpStatusCodes, adminRole, ViolationType, vendorOrderStatus } from '../../utils/constants';
import models from '../../models';
import { Op, Sequelize } from 'sequelize';
import { PolicyViolationsAttributes } from '../../models/policy_violations';
import moment from 'moment';
import mailServices from '../user/user_notifications/policy_violation_email_service';

class PolicyViolations {
    /**
     * @api {get} /v1/auth/admin/policy-violations
     * @apiName listPolicyViolations
     * @apiGroup PolicyViolations
     *
     * @apiQuery {Number} [page=1] Page number
     * @apiQuery {Number} [limit=10] Items per page
     * @apiQuery {String} [violation_type] Filter by violation type
     * @apiQuery {String} [vendor_id] Filter by vendor ID
     *
     * @apiSuccess {Object} PolicyViolations list with pagination
     */
    async listPolicyViolations(req: Request, res: Response, next: NextFunction) {
        try {
            const role = req['role'];
            const id = req['id'];

            const violationType = req.query.violation_type;
            const vendorId = req.query.vendor_id;
            const offset = parseInt(String(req.query.skip), 10);
            const limit = parseInt(String(req.query.limit), 0);

            const conditions: any = [];

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error('Unauthorized access');
            }

            if (violationType) {
                conditions.push({ violation_type: violationType });
            }

            if (vendorId) {
                conditions.push({ vendor_id: vendorId });
            }

            const { count, rows } = await models.policy_violations.findAndCountAll({
                where: { [Op.and]: conditions },
                order: [['createdAt', 'DESC']],
                limit,
                offset
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Policy violations retrieved successfully',
                data: rows,
                count
            });
        } catch (error: any) {
            logger.error(`Error listing policy violations: ${error.message}`);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/policy-violations/statistics
     * @apiName getPolicyViolationStatistics
     * @apiGroup PolicyViolations
     *
     * @apiQuery {String} [vendor_id] Filter statistics by vendor ID
     *
     * @apiSuccess {Object} Policy violation statistics
     */
    async getPolicyViolationStatistics(req: Request, res: Response, next: NextFunction) {
        try {
            const role = req['role'];

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error('Unauthorized access');
            }

            const vendor_id = req.query.vendor_id;
            const vendorFilter = vendor_id ? { vendor_id } : {};

            // Get violation counts
            const duplicateDiamondCount = await models.policy_violations.count({
                where: { ...vendorFilter, violation_type: ViolationType.DUPLICATE_DIAMOND, is_active: true }
            });

            const unfulfilledOrderCount = await models.policy_violations.count({
                where: { ...vendorFilter, violation_type: ViolationType.UNFULFILLED_ORDER, is_active: true }
            });

            const lateShipmentCount = await models.policy_violations.count({
                where: { ...vendorFilter, violation_type: ViolationType.LATE_SHIPMENT, is_active: true }
            });

            // Get sold diamonds count and value
            const soldOrdersQuery: any = {
                order_status: 'DELIVERED'
            };

            if (vendor_id) {
                soldOrdersQuery['$vendor_orders.vendor_id$'] = vendor_id;
            }

            const soldOrders = await models.orders.findAll({
                where: soldOrdersQuery,
                include: vendor_id ? [{ model: models.vendor_orders, where: { vendor_id } }] : [],
                attributes: ['amount']
            });

            const soldDiamondCount = soldOrders.length;
            const soldValue = soldOrders.reduce((sum, order) => sum + parseFloat(order.amount), 0);

            // Get return orders count
            const returnOrdersQuery: any = {};
            if (vendor_id) {
                returnOrdersQuery.vendor_id = vendor_id;
            }

            const returnDiamondCount = await models.return_orders.count({
                where: returnOrdersQuery
            });

            // Calculate ratios
            const unfulfillmentRatio = soldDiamondCount > 0 ? (unfulfilledOrderCount / soldDiamondCount) * 100 : 0;
            const fulfillmentRatio =
                soldDiamondCount > 0 ? ((soldDiamondCount - unfulfilledOrderCount) / soldDiamondCount) * 100 : 0;
            const returnRatio = soldDiamondCount > 0 ? (returnDiamondCount / soldDiamondCount) * 100 : 0;

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Policy violation statistics retrieved successfully',
                data: {
                    unfulfillmentRatio: Math.round(unfulfillmentRatio * 100) / 100,
                    fulfillmentRatio: Math.round(fulfillmentRatio * 100) / 100,
                    duplicateDiamondCount,
                    unfulfilledOrderCount,
                    returnDiamondCount,
                    returnRatio: Math.round(returnRatio * 100) / 100,
                    lateShipmentCount,
                    soldDiamondCount,
                    soldValue: Math.round(soldValue * 100) / 100
                }
            });
        } catch (error: any) {
            logger.error(`Error getting policy violation statistics: ${error.message}`);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/policy-violations/dashboard
     * @apiName getPolicyViolationDashboard
     * @apiGroup PolicyViolations
     *
     * @apiSuccess {Object} Dashboard data with violation counts and trends
     */
    async getPolicyViolationDashboard(req: Request, res: Response, next: NextFunction) {
        try {
            const role = req['role'];

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error('Unauthorized access');
            }

            // Get overall statistics
            //const overallStats = await policyViolationsService.getViolationStatistics();

            // Get recent violations (last 7 days)
            //const recentViolations = await policyViolationsService.getAllViolations(1, 10);

            // Get violation counts by type
            // const violationCounts = {
            //     duplicate_diamond: await this.getViolationCountByType('DUPLICATE_DIAMOND'),
            //     unfulfilled_order: await this.getViolationCountByType('UNFULFILLED_ORDER'),
            //     late_shipment: await this.getViolationCountByType('LATE_SHIPMENT')
            // };

            // Get top violating vendors
            //const topViolatingVendors = await this.getTopViolatingVendors();

            const dashboardData = {
                // overall_statistics: overallStats,
                // recent_violations: recentViolations.violations,
                // violation_counts: violationCounts,
                // top_violating_vendors: topViolatingVendors,
                // summary: {
                //     total_violations:
                //         violationCounts.duplicate_diamond +
                //         violationCounts.unfulfilled_order +
                //         violationCounts.late_shipment,
                //     active_violations: recentViolations.total
                // }
            };

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Policy violation dashboard data retrieved successfully',
                data: dashboardData
            });
        } catch (error: any) {
            logger.error(`Error getting policy violation dashboard: ${error.message}`);
            next(error);
        }
    }

    /**
     * Create a new policy violation
     */
    async createViolation(violationList: PolicyViolationsAttributes[]): Promise<any> {
        try {
            logger.info(`Creating policy violation: ${JSON.stringify(violationList)}`);

            /// bulk create violations object
            const bulkCreateObject = violationList.map((item: any) => ({
                vendor_id: item.vendor_id,
                violation_type: item.violation_type,
                stock_id: item.stock_id,
                order_id: item.order_id,
                details: item.details || {}
            }));

            await models.policy_violations.bulkCreate(bulkCreateObject, { ignoreDuplicates: true });

            logger.info(`Policy violation created`);
        } catch (error) {
            logger.error(`Error creating policy violation: ${error}`);
            throw error;
        }
    }

    /**
     * Check for duplicate diamonds and create violation if found
     */
    async checkDuplicateDiamond(createdStockObjects: any[], vendorId?: any, adminId?: any): Promise<void> {
        try {
            /// extract vendor_id, admin_id, certificate_number list
            const stockList = createdStockObjects?.map((item: any) => ({
                vendor_id: item?.vendor_id,
                admin_id: item?.admin_id,
                certificate_number: item?.certificate_number
            }));

            if (!stockList.length) {
                return;
            }

            /// list certificate number is already exists with different vendor or admin
            const checkExistingCertificates = await models.stocks.findAll({
                where: {
                    [Op.and]: [
                        { certificate_number: { [Op.in]: stockList?.map((item: any) => item?.certificate_number) } },
                        {
                            [Op.or]: [
                                vendorId ? { vendor_id: { [Op.ne]: vendorId } } : {},
                                adminId ? { admin_id: { [Op.ne]: adminId } } : {}
                            ]
                        }
                    ]
                },
                attributes: ['certificate_number', 'vendor_id', 'stock_id'],
                raw: true
            });

            /// extract duplicate certificates using stocklist and checkExistingCertificates
            const duplicateCertificates = stockList?.filter((item: any) =>
                checkExistingCertificates?.map((s: any) => s.certificate_number).includes(item.certificate_number)
            );

            if (!duplicateCertificates.length) {
                throw new Error('Duplicate certificates not found');
            }

            // Check for duplicate diamonds based on certificate number
            if (duplicateCertificates.length > 0) {
                /// create violation
                const violationList: PolicyViolationsAttributes[] = duplicateCertificates.map((item: any) => ({
                    vendor_id: vendorId,
                    violation_type: ViolationType.DUPLICATE_DIAMOND,
                    stock_id: item.stock_id,
                    details: {
                        certificate_number: item.certificate_number,
                        stock_id: item.stock_id,
                        vendor_id: item.vendor_id
                    }
                }));

                /// update duplicate_violation_count
                /// update once a day
                await models.policy_violations.update(
                    { duplicate_violation_count: Sequelize.literal('duplicate_violation_count + 1') },
                    {
                        where: {
                            [Op.and]: [
                                { violation_type: ViolationType.DUPLICATE_DIAMOND },
                                { vendor_id: vendorId },
                                { stock_id: { [Op.in]: duplicateCertificates?.map((item: any) => item?.stock_id) } },
                                Sequelize.where(
                                    Sequelize.fn('DATE', Sequelize.col('updatedAt')),
                                    '<',
                                    Sequelize.fn('CURRENT_DATE')
                                )
                            ]
                        }
                    }
                );

                /// create violation
                await this.createViolation(violationList);

                /// send email
                try {
                    /// send violation email
                    await mailServices.sendViolationNotification(violationList, vendorId);

                    ///
                } catch (error: any) {
                    logger.error(`Error sending duplicate diamond email: ${error}`);
                    // Don't throw error to avoid breaking the stock creation process
                }
            }
        } catch (error) {
            logger.error(`Error checking duplicate diamond: ${error}`);
            throw error;
        }
    }

    /**
     * Create unfulfilled order violation
     */
    async createUnfulfilledOrderViolation(order_id: string, return_stock_ids: any[]): Promise<void> {
        try {
            if (!return_stock_ids.length) {
                return;
            }

            const violationList: PolicyViolationsAttributes[] = return_stock_ids?.map((item: any) => ({
                vendor_id: item?.vendor_id,
                violation_type: ViolationType.UNFULFILLED_ORDER,
                order_id,
                stock_id: item?.stock_id,
                details: {
                    reason: item?.reason
                }
            }));

            /// create violation
            await this.createViolation(violationList);
        } catch (error) {
            logger.error(`Error creating unfulfilled order violation: ${error}`);
            throw error;
        }
    }

    /**
     * Check for late shipment and create violation if applicable
     */
    async checkLateShipment(vendor_order_id: string): Promise<void> {
        try {
            /// fetch vendor order
            const vendorOrder = await models.vendor_orders.findOne(
                { where: { id: vendor_order_id } },
                {
                    include: [{ model: models.orders }, { model: models.vendors }]
                }
            );

            if (!vendorOrder || !vendorOrder.orders) {
                return;
            }

            const orderCreatedAt = moment.utc(vendorOrder.orders.createdAt);
            const now = moment.utc();

            const hoursDiff = now.diff(orderCreatedAt, 'hours', true); // true = get fractional hours

            /// Check if shipped after 24 hours
            if (hoursDiff > 24 && vendorOrder.status === vendorOrderStatus.shipped) {
                logger.info(`Late shipment detected for order ${vendorOrder.order_id}`);

                /// create violation
                await this.createViolation([
                    {
                        vendor_id: vendorOrder.vendor_id,
                        violation_type: ViolationType.LATE_SHIPMENT,
                        order_id: vendorOrder.order_id,
                        stock_id: vendorOrder.stock_id,
                        details: {
                            hours_delayed: Math.round(hoursDiff),
                            order_created_at: orderCreatedAt,
                            shipped_at: now
                        }
                    }
                ]);
            }

            ///
        } catch (error) {
            logger.error(`Error checking late shipment: ${error}`);
            throw error;
        }
    }
}

export default new PolicyViolations();
