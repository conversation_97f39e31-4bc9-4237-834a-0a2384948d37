// import adminRoute from './admin/admin/auth';
import subAdminRoute from './admin/subAdmin/auth';
import { Router } from 'express';
import adminUserRoutes from '../routes/admin/user/auth';
import userRoutes from '../routes/user/user/auth';
import userStockRoutes from '../routes/user/stock/auth';
import buyRequestRoutes from './user/buy_request/auth';
import userBuyRequestRoutes from './user/user_buy_request/auth';
import creditHistoryRoutes from './user/credit_history/auth';
import vendorRoutes from '../routes/admin/vendor/auth';
import orderRoutes from './admin/order/auth';
import stockRoutes from '../routes/admin/stock/auth';
import settingsRoutes from '../routes/admin/settings/auth';
import adminDashboardRoutes from '../routes/admin/dashboard/auth';
import adminRoutes from '../routes/admin/banners/auth';
import userOrderRoutes from '../routes/user/order/auth';
import adminUnifiedOrderRoutes from '../routes/admin/unified_order/auth';
import userUnifiedOrderRoutes from '../routes/user/unified_order/auth';
import userShipmentsRoutes from '../routes/user/shipment/auth';
import userNotificationRoutes from '../routes/user/user_notification/auth';
import vendorNotificationRoutes from '../routes/admin/vendor_notification/auth';
import adminNotificationRoutes from '../routes/admin/admin_notification/auth';
import userWishlistRoutes from '../routes/user/wishlist_stock/auth';
import returnOrderRoutes from '../routes/admin/return_order/auth';
import returnUserOrderRoutes from '../routes/user/return_order/auth';
import userStockOfferRoutes from '../routes/user/stock_offer/auth';
import adminStockOfferRoutes from '../routes/admin/stock_offer/auth';
import adminStaticPagesRoutes from '../routes/admin/static_pages/auth';
import adminSalesReportingRoutes from '../routes/admin/sales_reporting/auth';
import vendorRejectedHoldRequestsRoutes from '../routes/admin/vendor_rejected_hold_requests/auth';
import userDashboardRoutes from '../routes/user/dashboard/auth';
import adminAuthRoutes from '../routes/admin/admin/auth';
import melleRoutes from '../routes/user/melle/auth';
import adminJewelleryInquiryAuthRoutes from '../routes/admin/jewellery_inquiry/auth';
import adminJewelleryShowcaseRoutes from '../routes/admin/jewellery_showcase/auth';
import userJewelleryShowcaseRoutes from '../routes/user/jewellery_showcase/auth';
import userCartRoutes from '../routes/user/cart/auth';
import shareLinkRoutes from '../routes/user/share_links/auth';
import policyViolationsRoutes from '../routes/admin/policy_violations/auth';

const router = Router();

/**
 * Total Auth Routes
 */
// router.use('/admin', adminRoleHandler, adminRoute);
router.use('/admin', subAdminRoute);

router.use('/admin', adminUserRoutes);

router.use('/admin', vendorRoutes);

router.use('/admin', orderRoutes);

router.use('/admin', stockRoutes);

router.use('/admin', settingsRoutes);

router.use('/admin', adminDashboardRoutes);

router.use('/admin', adminRoutes);

router.use('/admin', returnOrderRoutes);

router.use('/admin', adminStockOfferRoutes);

router.use('/admin', adminStaticPagesRoutes);

router.use('/admin', adminSalesReportingRoutes);

router.use('/admin', vendorRejectedHoldRequestsRoutes);

router.use('/admin', adminUnifiedOrderRoutes);

router.use('/user', userUnifiedOrderRoutes);

router.use('/user', returnUserOrderRoutes);

router.use('/user', userRoutes);

router.use('/user', userStockRoutes);

router.use('/user', userOrderRoutes);

router.use('/user', creditHistoryRoutes);

router.use('/user', userShipmentsRoutes);

router.use('/user', userBuyRequestRoutes);

router.use('/user', userCartRoutes);

router.use('/', buyRequestRoutes);

router.use('/admin', adminAuthRoutes);

router.use('/user', userNotificationRoutes);

router.use('/vendor', vendorNotificationRoutes);

router.use('/admin', adminNotificationRoutes);

router.use('/user', userWishlistRoutes);

router.use('/user', userStockOfferRoutes);

router.use('/user', userDashboardRoutes);

router.use('/user', melleRoutes);

router.use('/admin', adminJewelleryInquiryAuthRoutes);

router.use('/admin', adminJewelleryShowcaseRoutes);

router.use('/user', userJewelleryShowcaseRoutes);

router.use('/user', shareLinkRoutes);

router.use('/admin', policyViolationsRoutes);

export default router;
