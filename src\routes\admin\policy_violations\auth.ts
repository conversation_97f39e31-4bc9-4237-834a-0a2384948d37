import { IRouter, Router, Request, Response, NextFunction } from 'express';
import policyViolations from '../../../controllers/admin/policy_violations';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    policyViolationListSchema,
    policyViolationStatisticsSchema,
} from '../../../utils/schema/policy_violations_schema';

const routes: IRouter = Router();

/**
 * @api {get} /v1/auth/admin/policy-violations
 * @apiName listPolicyViolations
 * @apiGroup PolicyViolations
 * @apiDescription Get list of all policy violations with pagination and filtering
 */
routes.get(
    '/policy-violations',
    (req: Request, res: Response, next: NextFunction) =>
        fieldsValidator(req, res, next, policyViolationListSchema),
    policyViolations.listPolicyViolations
);

/**
 * @api {get} /v1/auth/admin/policy-violations/statistics
 * @apiName getPolicyViolationStatistics
 * @apiGroup PolicyViolations
 * @apiDescription Get comprehensive policy violation statistics
 */
routes.get(
    '/policy-violations/statistics',
    (req: Request, res: Response, next: NextFunction) =>
        fieldsValidator(req, res, next, policyViolationStatisticsSchema),
    policyViolations.getPolicyViolationStatistics
);

/**
 * @api {get} /v1/auth/admin/policy-violations/dashboard
 * @apiName getPolicyViolationDashboard
 * @apiGroup PolicyViolations
 * @apiDescription Get dashboard data for policy violations
 */
routes.get(
    '/policy-violations/dashboard',
    policyViolations.getPolicyViolationDashboard
);




export default routes;
