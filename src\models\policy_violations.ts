import { cert } from 'firebase-admin/app';
import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface PolicyViolationsAttributes {
    id?: string;
    vendor_id: string;
    violation_type: string; // DUPLICATE_DIAMOND, UNFULFILLED_ORDER, LATE_SHIPMENT
    stock_id?: string;
    order_id?: string;
    details?: object;
    duplicate_violation_count?: number;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface PolicyViolationsCreationAttributes extends Optional<PolicyViolationsAttributes, 'id'> {}

interface PolicyViolationsInstance
    extends Model<PolicyViolationsAttributes, PolicyViolationsCreationAttributes>,
        PolicyViolationsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type PolicyViolationsStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => PolicyViolationsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const policy_violations = sequelize.define<PolicyViolationsInstance>(
        'policy_violations',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            violation_type: {
                type: DataTypes.ENUM('DUPLICATE_DIAMOND', 'UNFULFILLED_ORDER', 'LATE_SHIPMENT'),
                allowNull: false
            },
            stock_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            duplicate_violation_count: {
                type: DataTypes.INTEGER,
                allowNull: true,
                defaultValue: 1
            },
            details: {
                type: DataTypes.JSONB,
                allowNull: true,
                defaultValue: {}
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            indexes: [
                {
                    name: 'unique_violation_type_and_stock_id_and_vendor_id',
                    fields: ['violation_type', 'stock_id', 'vendor_id'],
                    unique: true
                }
            ],
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as PolicyViolationsStatic;

    // TODO: make common function to sync
    // await policy_violations.sync({ alter: true });

    return policy_violations;
};
