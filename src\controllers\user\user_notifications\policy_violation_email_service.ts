import mailServices from './mail_services';
import { logger } from '../../../utils/logger';
import models from '../../../models';
import { ViolationType } from '../../../utils/constants';
import { PolicyViolationsAttributes } from 'models/policy_violations';

export interface ViolationEmailData {
    vendorName: string;
    companyName: string;
    violationType: string;
    violationDetails: any;
    stockDetails?: any;
    orderDetails?: any;
}

class PolicyViolationEmailService {
    /**
     * Send email notification for policy violation
     */
    async sendViolationNotification(
        violationsList: PolicyViolationsAttributes[],
        vendorId: any,
    ): Promise<void> {
        try {
            if (!violationsList.length) {
                return;
            }

            const vendor = await models.vendors.findOne({
                where: { id: vendorId },
                attributes: ['email', 'first_name', 'last_name']
            });


            if (!vendor) {
                throw new Error('Vendor not found');
            }

            const subject = 'Policy Violation Alert: Duplicate Diamond Detected';

            let content = `
            <div style="background-color: #ffebee; border: 1px solid #ffcdd2; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="color: #c62828; margin-top: 0;">Duplicate Diamond Detected</h3>
                <p>We have identified that you have listed a diamond that appears to be a duplicate of an existing listing.</p>
        `;

            if (violationsList?.length) {
                content += `
                <p><strong>Certificate Numbers:</strong></p>
                <ul>
                ${violationsList.map((item) => `<li>${(item?.details as any).certificate_number}</li>`).join('')}
                </ul>
            `;
            }

            content += `
                <p><strong>Action Required:</strong> Please review your inventory and remove any duplicate listings to maintain data integrity.</p>
            </div>
        `;

            await mailServices.send({
                to: vendor.email,
                subject,
                data: {
                    name: `${vendor.first_name} ${vendor.last_name}`,
                    message: content
                }
            });

            logger.info(`Policy violation email sent to vendor: ${vendor.email}`);
        } catch (error) {
            logger.error(`Error sending policy violation email: ${error}`);
            throw error;
        }
    }
}

export default new PolicyViolationEmailService();
